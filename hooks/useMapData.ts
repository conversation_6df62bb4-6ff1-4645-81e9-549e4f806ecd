'use client';

import { useState, useEffect, useMemo } from 'react';
import { Location, Alert, Route, TripStatistics, SearchFilters, LocationType } from '@/types/map';

interface UseMapDataProps {
  initialLocations?: Location[];
  initialAlerts?: Alert[];
  initialRoutes?: Route[];
  initialStatistics?: TripStatistics;
}

interface UseMapDataReturn {
  locations: Location[];
  alerts: Alert[];
  routes: Route[];
  statistics: TripStatistics;
  filteredLocations: Location[];
  searchFilters: SearchFilters;
  isLoading: boolean;
  error: string | null;
  updateLocations: (locations: Location[]) => void;
  updateAlerts: (alerts: Alert[]) => void;
  updateRoutes: (routes: Route[]) => void;
  updateStatistics: (statistics: TripStatistics) => void;
  setSearchFilters: (filters: SearchFilters) => void;
  resetFilters: () => void;
}

const DEFAULT_STATISTICS: TripStatistics = {
  activeTrips: 0,
  activeTripsWithAlerts: 0,
  activeTripsWithoutAlerts: 0,
  activeTripsWithCommunicationLost: 0
};

const DEFAULT_FILTERS: SearchFilters = {
  showPort: true,
  showCheckpost: true,
  tripCode: 'Trip Code',
  searchValue: ''
};

export function useMapData({
  initialLocations = [],
  initialAlerts = [],
  initialRoutes = [],
  initialStatistics = DEFAULT_STATISTICS
}: UseMapDataProps = {}): UseMapDataReturn {
  
  const [locations, setLocations] = useState<Location[]>(initialLocations);
  const [alerts, setAlerts] = useState<Alert[]>(initialAlerts);
  const [routes, setRoutes] = useState<Route[]>(initialRoutes);
  const [statistics, setStatistics] = useState<TripStatistics>(initialStatistics);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>(DEFAULT_FILTERS);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filter locations based on search criteria
  const filteredLocations = useMemo(() => {
    let filtered = [...locations];

    // Apply location type filters
    if (!searchFilters.showPort) {
      filtered = filtered.filter(loc => 
        !['seaport', 'landport', 'airport'].includes(loc.type)
      );
    }

    if (!searchFilters.showCheckpost) {
      filtered = filtered.filter(loc => 
        !['checkpoint', 'police_station'].includes(loc.type)
      );
    }

    // Apply search value filter
    if (searchFilters.searchValue.trim()) {
      const searchTerm = searchFilters.searchValue.toLowerCase().trim();
      filtered = filtered.filter(loc =>
        loc.name.toLowerCase().includes(searchTerm) ||
        loc.type.toLowerCase().includes(searchTerm) ||
        (loc.description && loc.description.toLowerCase().includes(searchTerm))
      );
    }

    // Apply location type filter if specified
    if (searchFilters.locationTypes && searchFilters.locationTypes.length > 0) {
      filtered = filtered.filter(loc => 
        searchFilters.locationTypes!.includes(loc.type)
      );
    }

    return filtered;
  }, [locations, searchFilters]);

  // Update functions
  const updateLocations = (newLocations: Location[]) => {
    setLocations(newLocations);
  };

  const updateAlerts = (newAlerts: Alert[]) => {
    setAlerts(newAlerts);
  };

  const updateRoutes = (newRoutes: Route[]) => {
    setRoutes(newRoutes);
  };

  const updateStatistics = (newStatistics: TripStatistics) => {
    setStatistics(newStatistics);
  };

  const resetFilters = () => {
    setSearchFilters(DEFAULT_FILTERS);
  };

  // Auto-update statistics based on alerts
  useEffect(() => {
    const alertCount = alerts.length;
    const highPriorityAlerts = alerts.filter(alert => alert.severity === 'high').length;
    
    setStatistics(prev => ({
      ...prev,
      activeTripsWithAlerts: Math.max(alertCount, prev.activeTripsWithAlerts),
      activeTripsWithoutAlerts: Math.max(
        prev.activeTrips - alertCount, 
        prev.activeTripsWithoutAlerts
      )
    }));
  }, [alerts]);

  return {
    locations,
    alerts,
    routes,
    statistics,
    filteredLocations,
    searchFilters,
    isLoading,
    error,
    updateLocations,
    updateAlerts,
    updateRoutes,
    updateStatistics,
    setSearchFilters,
    resetFilters
  };
}
