"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import {
  Location,
  Alert,
  Route,
  TripStatistics,
  SearchFilters,
  LocationType,
} from "@/types/map";

interface UseMapDataProps {
  initialLocations?: Location[];
  initialAlerts?: Alert[];
  initialRoutes?: Route[];
  initialStatistics?: TripStatistics;
}

interface UseMapDataReturn {
  locations: Location[];
  alerts: Alert[];
  routes: Route[];
  statistics: TripStatistics;
  filteredLocations: Location[];
  searchFilters: SearchFilters;
  isLoading: boolean;
  error: string | null;
  updateLocations: (locations: Location[]) => void;
  updateAlerts: (alerts: Alert[]) => void;
  updateRoutes: (routes: Route[]) => void;
  updateStatistics: (statistics: TripStatistics) => void;
  setSearchFilters: (filters: SearchFilters) => void;
  resetFilters: () => void;
}

const DEFAULT_STATISTICS: TripStatistics = {
  activeTrips: 0,
  activeTripsWithAlerts: 0,
  activeTripsWithoutAlerts: 0,
  activeTripsWithCommunicationLost: 0,
};

const DEFAULT_FILTERS: SearchFilters = {
  showPort: true,
  showCheckpost: true,
  tripCode: "Trip Code",
  searchValue: "",
};

export function useMapData({
  initialLocations = [],
  initialAlerts = [],
  initialRoutes = [],
  initialStatistics = DEFAULT_STATISTICS,
}: UseMapDataProps = {}): UseMapDataReturn {
  const [locations, setLocations] = useState<Location[]>(initialLocations);
  const [alerts, setAlerts] = useState<Alert[]>(initialAlerts);
  const [routes, setRoutes] = useState<Route[]>(initialRoutes);
  const [statistics, setStatistics] =
    useState<TripStatistics>(initialStatistics);
  const [searchFilters, setSearchFilters] =
    useState<SearchFilters>(DEFAULT_FILTERS);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Optimized filter function with performance improvements
  const filteredLocations = useMemo(() => {
    console.log(
      `🔍 Filtering ${locations.length} locations with filters:`,
      searchFilters
    );

    // Early return if no locations
    if (locations.length === 0) {
      return [];
    }

    let filtered = locations;

    // Apply location type filters efficiently
    if (!searchFilters.showPort || !searchFilters.showCheckpost) {
      filtered = filtered.filter((loc) => {
        const isPort = ["seaport", "landport", "airport"].includes(loc.type);
        const isCheckpost = ["checkpoint", "police_station"].includes(loc.type);

        return (
          (searchFilters.showPort || !isPort) &&
          (searchFilters.showCheckpost || !isCheckpost)
        );
      });
    }

    // Apply search value filter with optimized string matching
    const searchTerm = searchFilters.searchValue.trim().toLowerCase();
    if (searchTerm) {
      filtered = filtered.filter((loc) => {
        const nameMatch = loc.name.toLowerCase().includes(searchTerm);
        const typeMatch = loc.type.toLowerCase().includes(searchTerm);
        const descMatch =
          loc.description?.toLowerCase().includes(searchTerm) ?? false;

        return nameMatch || typeMatch || descMatch;
      });
    }

    // Apply specific location type filter if specified
    if (searchFilters.locationTypes?.length) {
      filtered = filtered.filter((loc) =>
        searchFilters.locationTypes!.includes(loc.type)
      );
    }

    console.log(`✅ Filtered to ${filtered.length} locations`);
    return filtered;
  }, [locations, searchFilters]);

  // Update functions
  const updateLocations = (newLocations: Location[]) => {
    setLocations(newLocations);
  };

  const updateAlerts = (newAlerts: Alert[]) => {
    setAlerts(newAlerts);
  };

  const updateRoutes = (newRoutes: Route[]) => {
    setRoutes(newRoutes);
  };

  const updateStatistics = (newStatistics: TripStatistics) => {
    setStatistics(newStatistics);
  };

  const resetFilters = () => {
    setSearchFilters(DEFAULT_FILTERS);
  };

  // Auto-update statistics based on alerts
  useEffect(() => {
    const alertCount = alerts.length;
    const highPriorityAlerts = alerts.filter(
      (alert) => alert.severity === "high"
    ).length;

    setStatistics((prev) => ({
      ...prev,
      activeTripsWithAlerts: Math.max(alertCount, prev.activeTripsWithAlerts),
      activeTripsWithoutAlerts: Math.max(
        prev.activeTrips - alertCount,
        prev.activeTripsWithoutAlerts
      ),
    }));
  }, [alerts]);

  return {
    locations,
    alerts,
    routes,
    statistics,
    filteredLocations,
    searchFilters,
    isLoading,
    error,
    updateLocations,
    updateAlerts,
    updateRoutes,
    updateStatistics,
    setSearchFilters,
    resetFilters,
  };
}
