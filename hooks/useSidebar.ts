'use client';

import { useState, useCallback } from 'react';

export type SidebarTab = 'search' | 'alerts' | 'routes';

interface UseSidebarProps {
  initialOpen?: boolean;
  initialTab?: SidebarTab;
}

interface UseSidebarReturn {
  isOpen: boolean;
  activeTab: SidebarTab;
  openSidebar: () => void;
  closeSidebar: () => void;
  toggleSidebar: () => void;
  setActiveTab: (tab: SidebarTab) => void;
  openWithTab: (tab: SidebarTab) => void;
}

export function useSidebar({
  initialOpen = false,
  initialTab = 'search'
}: UseSidebarProps = {}): UseSidebarReturn {
  
  const [isOpen, setIsOpen] = useState(initialOpen);
  const [activeTab, setActiveTab] = useState<SidebarTab>(initialTab);

  const openSidebar = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeSidebar = useCallback(() => {
    setIsOpen(false);
  }, []);

  const toggleSidebar = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const handleSetActiveTab = useCallback((tab: SidebarTab) => {
    setActiveTab(tab);
  }, []);

  const openWithTab = useCallback((tab: SidebarTab) => {
    setActiveTab(tab);
    setIsOpen(true);
  }, []);

  return {
    isOpen,
    activeTab,
    openSidebar,
    closeSidebar,
    toggleSidebar,
    setActiveTab: handleSetActiveTab,
    openWithTab
  };
}
