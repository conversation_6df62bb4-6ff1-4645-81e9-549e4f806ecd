// Optimized Google Maps API loader with performance enhancements

// Global state for Google Maps API loading with performance tracking
let isLoaded = false;
let isLoading = false;
let loadPromise: Promise<void> | null = null;
let loadStartTime: number = 0;

/**
 * Preconnects to Google Maps domains for faster loading
 */
function preconnectGoogleMaps(): void {
  if (typeof document === 'undefined') return;

  const domains = [
    'https://maps.googleapis.com',
    'https://maps.gstatic.com',
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com'
  ];

  domains.forEach(domain => {
    // Check if preconnect already exists
    const existingLink = document.querySelector(`link[href="${domain}"]`);
    if (!existingLink) {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    }
  });
}

/**
 * Loads the Google Maps JavaScript API with performance optimizations
 * @returns Promise that resolves when the API is loaded
 */
export async function loadOptimizedGoogleMapsAPI(): Promise<void> {
  // Return immediately if already loaded
  if (isLoaded) {
    console.log('✅ Google Maps API already loaded');
    return Promise.resolve();
  }

  // Return existing promise if currently loading
  if (isLoading && loadPromise) {
    console.log('🔄 Google Maps API loading in progress...');
    return loadPromise;
  }

  // Start performance tracking
  loadStartTime = performance.now();
  console.log('🚀 Starting optimized Google Maps API load...');

  // Preconnect to Google domains for faster loading
  preconnectGoogleMaps();

  // Create new loading promise
  loadPromise = new Promise((resolve, reject) => {
    // Check if already loaded (race condition protection)
    if (window.google && window.google.maps) {
      isLoaded = true;
      const loadTime = performance.now() - loadStartTime;
      console.log(`✅ Google Maps API was already available (${loadTime.toFixed(2)}ms)`);
      resolve();
      return;
    }

    isLoading = true;

    const script = document.createElement('script');
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      isLoading = false;
      console.error('❌ Google Maps API key is not configured');
      reject(new Error('Google Maps API key is not configured'));
      return;
    }

    // Optimized script URL with minimal libraries and async loading
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry&loading=async&callback=__googleMapsCallback`;
    script.async = true;
    script.defer = true;
    
    // Global callback for faster initialization
    (window as any).__googleMapsCallback = () => {
      const loadTime = performance.now() - loadStartTime;
      console.log(`✅ Google Maps API loaded successfully in ${loadTime.toFixed(2)}ms`);
      isLoaded = true;
      isLoading = false;
      delete (window as any).__googleMapsCallback;
      resolve();
    };
    
    script.onerror = (error) => {
      const loadTime = performance.now() - loadStartTime;
      console.error(`❌ Failed to load Google Maps API after ${loadTime.toFixed(2)}ms`, error);
      isLoading = false;
      delete (window as any).__googleMapsCallback;
      reject(new Error('Failed to load Google Maps API'));
    };

    document.head.appendChild(script);
  });

  return loadPromise;
}

/**
 * Gets the current loading state
 */
export function getGoogleMapsLoadingState() {
  return {
    isLoaded,
    isLoading,
    hasPromise: !!loadPromise
  };
}

/**
 * Resets the loading state (useful for testing)
 */
export function resetGoogleMapsLoadingState() {
  isLoaded = false;
  isLoading = false;
  loadPromise = null;
  loadStartTime = 0;
}

/**
 * Optimized map configuration for Saudi Arabia
 */
export const OPTIMIZED_MAP_CONFIG = {
  // Performance optimizations
  gestureHandling: "greedy" as const,
  zoomControl: true,
  mapTypeControl: false,
  scaleControl: false,
  streetViewControl: false,
  rotateControl: false,
  fullscreenControl: true,
  disableDefaultUI: false,
  
  // Restrict map bounds to Saudi Arabia for better performance
  restriction: {
    latLngBounds: {
      north: 32.5,
      south: 16.0,
      west: 34.0,
      east: 55.0,
    },
    strictBounds: false,
  },
  
  // Optimized styles for better performance
  styles: [
    {
      featureType: 'poi',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }]
    },
    {
      featureType: 'transit',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }]
    }
  ] as google.maps.MapTypeStyle[]
};

/**
 * Creates an optimized marker with performance enhancements
 */
export function createOptimizedMarker(
  location: { lat: number; lng: number; name: string; type: string },
  map: google.maps.Map,
  icon?: google.maps.Icon
): google.maps.Marker {
  return new google.maps.Marker({
    position: { lat: location.lat, lng: location.lng },
    map: map,
    title: location.name,
    icon: icon,
    optimized: true,
    animation: google.maps.Animation.DROP,
  });
}

/**
 * Batch processes markers for better performance
 */
export function batchProcessMarkers<T>(
  items: T[],
  processor: (item: T) => void,
  batchSize: number = 10,
  onComplete?: () => void
): void {
  let currentBatch = 0;
  
  const processBatch = () => {
    const start = currentBatch * batchSize;
    const end = Math.min(start + batchSize, items.length);
    
    for (let i = start; i < end; i++) {
      processor(items[i]);
    }
    
    currentBatch++;
    
    if (end < items.length) {
      // Process next batch asynchronously
      setTimeout(processBatch, 0);
    } else {
      // All items processed
      if (onComplete) {
        onComplete();
      }
    }
  };

  processBatch();
}

/**
 * Calculates optimal map center and zoom for given locations
 */
export function calculateOptimalMapView(locations: { lat: number; lng: number }[]) {
  if (locations.length === 0) {
    return {
      center: { lat: 24.7136, lng: 46.6753 }, // Riyadh
      zoom: 6
    };
  }

  if (locations.length === 1) {
    return {
      center: locations[0],
      zoom: 10
    };
  }

  // Calculate bounds
  const bounds = new google.maps.LatLngBounds();
  locations.forEach(location => {
    bounds.extend(new google.maps.LatLng(location.lat, location.lng));
  });

  const center = bounds.getCenter();
  
  // Calculate appropriate zoom level
  const ne = bounds.getNorthEast();
  const sw = bounds.getSouthWest();
  const latRange = ne.lat() - sw.lat();
  const lngRange = ne.lng() - sw.lng();
  const maxRange = Math.max(latRange, lngRange);
  
  let zoom = 10;
  if (maxRange > 10) zoom = 5;
  else if (maxRange > 5) zoom = 6;
  else if (maxRange > 2) zoom = 7;
  else if (maxRange > 1) zoom = 8;
  else if (maxRange > 0.5) zoom = 9;

  return {
    center: { lat: center.lat(), lng: center.lng() },
    zoom
  };
}

/**
 * Performance monitoring utilities
 */
export const MapPerformanceMonitor = {
  startTime: 0,
  
  start() {
    this.startTime = performance.now();
  },
  
  end(label: string) {
    const duration = performance.now() - this.startTime;
    console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
    return duration;
  },
  
  measure(fn: () => void, label: string) {
    this.start();
    fn();
    return this.end(label);
  }
};
