"use client";

import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import {
  Location,
  MapComponentProps,
  MapConfig,
  LocationType,
} from "@/types/map";
import { createMarkerIcon } from "@/utils/mapUtils";
import { loadGoogleMapsAPI } from "@/utils/googleMaps";

const DEFAULT_MAP_CONFIG: MapConfig = {
  center: { lat: 24.7136, lng: 46.6753 }, // Riyadh
  zoom: 6,
};

export default function MapComponent({
  locations,
  config = {},
  onLocationClick,
  onMapReady,
  className = "",
}: MapComponentProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);

  // Memoize map config to prevent unnecessary re-renders
  const mapConfig = useMemo(
    () => ({ ...DEFAULT_MAP_CONFIG, ...config }),
    [config]
  );

  // Load Google Maps API with improved error handling
  useEffect(() => {
    if (typeof window === "undefined") return;

    // Check if already loaded
    if (window.google && window.google.maps) {
      console.log("✅ MapComponent: Google Maps already loaded");
      setIsLoaded(true);
      return;
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    if (!apiKey) {
      console.error("❌ MapComponent: Google Maps API key is missing");
      return;
    }

    console.log("🔄 MapComponent: Loading Google Maps API...");

    // Use optimized script loading with preconnect
    const preconnectLink = document.createElement("link");
    preconnectLink.rel = "preconnect";
    preconnectLink.href = "https://maps.googleapis.com";
    document.head.appendChild(preconnectLink);

    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      // Add a small delay to ensure Google Maps is fully initialized
      setTimeout(() => {
        if (window.google && window.google.maps && window.google.maps.Map) {
          console.log("✅ MapComponent: Google Maps API loaded successfully");
          setIsLoaded(true);
        } else {
          console.error(
            "❌ MapComponent: Google Maps API not properly initialized"
          );
        }
      }, 100);
    };

    script.onerror = (error) => {
      console.error("❌ MapComponent: Failed to load Google Maps API", error);
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script if component unmounts
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
      if (preconnectLink.parentNode) {
        preconnectLink.parentNode.removeChild(preconnectLink);
      }
    };
  }, []);

  // Initialize map with performance optimizations
  useEffect(() => {
    if (!isLoaded || !mapRef.current) return;

    console.log("🗺️ MapComponent: Initializing map...");

    const mapInstance = new google.maps.Map(mapRef.current, {
      center: mapConfig.center,
      zoom: mapConfig.zoom,
      styles: mapConfig.styles,
      // Performance optimizations
      gestureHandling: "greedy",
      zoomControl: true,
      mapTypeControl: false,
      scaleControl: false,
      streetViewControl: false,
      rotateControl: false,
      fullscreenControl: true,
      // Disable default UI for faster loading
      disableDefaultUI: false,
      // Enable optimized rendering
      optimized: true,
      // Restrict map bounds to Saudi Arabia for better performance
      restriction: {
        latLngBounds: {
          north: 32.5,
          south: 16.0,
          west: 34.0,
          east: 55.0,
        },
        strictBounds: false,
      },
    });

    // Add performance monitoring
    const startTime = performance.now();
    mapInstance.addListener("idle", () => {
      const loadTime = performance.now() - startTime;
      console.log(
        `✅ MapComponent: Map fully loaded in ${loadTime.toFixed(2)}ms`
      );
    });

    setMap(mapInstance);

    if (onMapReady) {
      onMapReady(mapInstance);
    }
  }, [isLoaded, mapConfig, onMapReady]);

  // Helper function to create info window content
  const createInfoWindowContent = (location: Location): string => {
    return `
      <div style="padding: 8px; min-width: 200px;">
        <h3 style="font-weight: bold; font-size: 14px; margin-bottom: 4px; color: #1f2937;">
          ${location.name}
        </h3>
        <p style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">
          Type: ${location.type.replace("_", " ").toUpperCase()}
        </p>
        <p style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">
          Coordinates: ${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}
        </p>
        ${
          location.description
            ? `
          <p style="font-size: 12px; color: #374151; margin-top: 8px;">
            ${location.description}
          </p>
        `
            : ""
        }
      </div>
    `;
  };

  // Memoize marker creation function for better performance
  const createMarker = useCallback(
    (location: Location) => {
      const marker = new google.maps.Marker({
        position: { lat: location.lat, lng: location.lng },
        map: map,
        title: location.name,
        icon: createMarkerIcon(location.type),
        optimized: true, // Enable marker optimization
        animation: google.maps.Animation.DROP, // Add smooth animation
      });

      // Create info window lazily (only when needed)
      let infoWindow: google.maps.InfoWindow | null = null;

      marker.addListener("click", () => {
        // Create info window only when clicked (lazy loading)
        if (!infoWindow) {
          infoWindow = new google.maps.InfoWindow({
            content: createInfoWindowContent(location),
            maxWidth: 300,
          });
        }

        // Close other info windows before opening new one
        markers.forEach((m) => {
          const existingInfoWindow = (m as any).infoWindow;
          if (existingInfoWindow) {
            existingInfoWindow.close();
          }
        });

        infoWindow.open(map, marker);

        if (onLocationClick) {
          onLocationClick(location);
        }
      });

      // Store info window reference for cleanup
      (marker as any).infoWindow = infoWindow;
      return marker;
    },
    [map, onLocationClick, markers]
  );

  // Add markers with performance optimizations
  useEffect(() => {
    if (!map) {
      console.log("⏳ MapComponent: No map instance yet");
      return;
    }

    if (!locations.length) {
      console.log("⏳ MapComponent: No locations to display");
      // Clear existing markers if no locations
      markers.forEach((marker) => {
        marker.setMap(null);
        // Clean up info window
        const infoWindow = (marker as any).infoWindow;
        if (infoWindow) {
          infoWindow.close();
        }
      });
      setMarkers([]);
      return;
    }

    console.log(`🗺️ MapComponent: Adding ${locations.length} markers to map`);

    // Clear existing markers efficiently
    markers.forEach((marker) => {
      marker.setMap(null);
      // Clean up info windows
      const infoWindow = (marker as any).infoWindow;
      if (infoWindow) {
        infoWindow.close();
      }
    });

    // Create new markers with batch processing for better performance
    const newMarkers: google.maps.Marker[] = [];

    // Process markers in batches to avoid blocking UI
    const batchSize = 10;
    let currentBatch = 0;

    const processBatch = () => {
      const start = currentBatch * batchSize;
      const end = Math.min(start + batchSize, locations.length);

      for (let i = start; i < end; i++) {
        const marker = createMarker(locations[i]);
        newMarkers.push(marker);
      }

      currentBatch++;

      if (end < locations.length) {
        // Process next batch asynchronously
        setTimeout(processBatch, 0);
      } else {
        // All markers processed
        setMarkers(newMarkers);
        console.log(
          `✅ MapComponent: ${newMarkers.length} markers added successfully`
        );
      }
    };

    processBatch();
  }, [map, locations, createMarker]);

  if (!isLoaded) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 ${className}`}
      >
        <div className="bg-white rounded-lg border p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Loading Map
          </h3>
          <p className="text-gray-500">Loading Google Maps...</p>
        </div>
      </div>
    );
  }

  return <div ref={mapRef} className={`w-full h-full ${className}`} />;
}
