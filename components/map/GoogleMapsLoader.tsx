"use client";

import { useEffect, useState } from 'react';

// Global state to prevent multiple loads
let isGoogleMapsLoaded = false;
let isGoogleMapsLoading = false;
let googleMapsPromise: Promise<void> | null = null;

interface GoogleMapsLoaderProps {
  children: (isLoaded: boolean) => React.ReactNode;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

export default function GoogleMapsLoader({ 
  children, 
  onLoad, 
  onError 
}: GoogleMapsLoaderProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Check if already loaded
    if (isGoogleMapsLoaded && window.google?.maps?.Map) {
      console.log('✅ GoogleMapsLoader: Already loaded');
      setIsLoaded(true);
      onLoad?.();
      return;
    }

    // If currently loading, wait for existing promise
    if (isGoogleMapsLoading && googleMapsPromise) {
      googleMapsPromise
        .then(() => {
          setIsLoaded(true);
          onLoad?.();
        })
        .catch((err) => {
          setError(err);
          onError?.(err);
        });
      return;
    }

    // Start loading
    const loadGoogleMaps = async () => {
      try {
        isGoogleMapsLoading = true;
        
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
        if (!apiKey) {
          throw new Error('Google Maps API key is not configured');
        }

        console.log('🔄 GoogleMapsLoader: Loading Google Maps API...');

        // Add preconnect for faster loading
        const preconnectLink = document.createElement('link');
        preconnectLink.rel = 'preconnect';
        preconnectLink.href = 'https://maps.googleapis.com';
        preconnectLink.crossOrigin = 'anonymous';
        document.head.appendChild(preconnectLink);

        // Load the script
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry`;
        script.async = true;
        script.defer = true;

        const loadPromise = new Promise<void>((resolve, reject) => {
          script.onload = () => {
            // Wait a bit for Google Maps to fully initialize
            setTimeout(() => {
              if (window.google?.maps?.Map) {
                console.log('✅ GoogleMapsLoader: Google Maps API loaded successfully');
                isGoogleMapsLoaded = true;
                isGoogleMapsLoading = false;
                resolve();
              } else {
                const error = new Error('Google Maps API failed to initialize properly');
                console.error('❌ GoogleMapsLoader:', error.message);
                isGoogleMapsLoading = false;
                reject(error);
              }
            }, 150);
          };

          script.onerror = () => {
            const error = new Error('Failed to load Google Maps API script');
            console.error('❌ GoogleMapsLoader:', error.message);
            isGoogleMapsLoading = false;
            reject(error);
          };
        });

        document.head.appendChild(script);
        
        await loadPromise;
        setIsLoaded(true);
        onLoad?.();

      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error loading Google Maps');
        console.error('❌ GoogleMapsLoader: Error:', error);
        setError(error);
        onError?.(error);
        isGoogleMapsLoading = false;
      }
    };

    googleMapsPromise = loadGoogleMaps();
  }, [onLoad, onError]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50">
        <div className="bg-white rounded-lg shadow-lg border p-6 text-center max-w-md">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Failed to Load Maps
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            {error.message}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return <>{children(isLoaded)}</>;
}

// Hook for using Google Maps loader
export function useGoogleMaps() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    if (isGoogleMapsLoaded && window.google?.maps?.Map) {
      setIsLoaded(true);
      return;
    }

    const checkGoogleMaps = () => {
      if (window.google?.maps?.Map) {
        setIsLoaded(true);
      } else {
        setTimeout(checkGoogleMaps, 100);
      }
    };

    checkGoogleMaps();
  }, []);

  return { isLoaded, error };
}

// Utility function to check if Google Maps is ready
export function isGoogleMapsReady(): boolean {
  return typeof window !== 'undefined' && 
         isGoogleMapsLoaded && 
         !!window.google?.maps?.Map;
}

// Reset function for testing
export function resetGoogleMapsLoader() {
  isGoogleMapsLoaded = false;
  isGoogleMapsLoading = false;
  googleMapsPromise = null;
}
