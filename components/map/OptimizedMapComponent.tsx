"use client";

import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import {
  Location,
  MapComponentProps,
  MapConfig,
} from "@/types/map";
import { createMarkerIcon } from "@/utils/mapUtils";
import {
  loadOptimizedGoogleMapsAPI,
  OPTIMIZED_MAP_CONFIG,
  batchProcessMarkers,
  calculateOptimalMapView,
  MapPerformanceMonitor,
} from "@/utils/optimizedGoogleMaps";

const DEFAULT_MAP_CONFIG: MapConfig = {
  center: { lat: 24.7136, lng: 46.6753 }, // Riyadh
  zoom: 6,
};

export default function OptimizedMapComponent({
  locations,
  config = {},
  onLocationClick,
  onMapReady,
  className = "",
}: MapComponentProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [isLoadingMarkers, setIsLoadingMarkers] = useState(false);

  // Memoize map config to prevent unnecessary re-renders
  const mapConfig = useMemo(() => {
    const optimal = locations.length > 0 ? calculateOptimalMapView(locations) : null;
    return {
      ...DEFAULT_MAP_CONFIG,
      ...OPTIMIZED_MAP_CONFIG,
      ...config,
      ...(optimal && { center: optimal.center, zoom: optimal.zoom }),
    };
  }, [config, locations]);

  // Load Google Maps API with optimizations
  useEffect(() => {
    if (typeof window === "undefined") return;

    // Check if already loaded
    if (window.google && window.google.maps) {
      console.log("✅ OptimizedMapComponent: Google Maps already loaded");
      setIsLoaded(true);
      return;
    }

    console.log("🔄 OptimizedMapComponent: Loading Google Maps API...");
    MapPerformanceMonitor.start();

    loadOptimizedGoogleMapsAPI()
      .then(() => {
        MapPerformanceMonitor.end("Google Maps API Load");
        setIsLoaded(true);
      })
      .catch((error) => {
        console.error("❌ OptimizedMapComponent: Failed to load Google Maps API", error);
      });
  }, []);

  // Initialize map with performance optimizations
  useEffect(() => {
    if (!isLoaded || !mapRef.current) return;

    console.log("🗺️ OptimizedMapComponent: Initializing map...");
    MapPerformanceMonitor.start();

    const mapInstance = new google.maps.Map(mapRef.current, mapConfig);

    // Add performance monitoring
    mapInstance.addListener("idle", () => {
      MapPerformanceMonitor.end("Map Initialization");
    });

    // Add bounds change listener for performance monitoring
    mapInstance.addListener("bounds_changed", () => {
      console.log("🔄 Map bounds changed");
    });

    setMap(mapInstance);

    if (onMapReady) {
      onMapReady(mapInstance);
    }
  }, [isLoaded, mapConfig, onMapReady]);

  // Helper function to create info window content
  const createInfoWindowContent = useCallback((location: Location): string => {
    return `
      <div style="padding: 12px; min-width: 250px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px; color: #1f2937;">
          ${location.name}
        </h3>
        <p style="font-size: 13px; color: #6b7280; margin-bottom: 6px;">
          <strong>Type:</strong> ${location.type.replace('_', ' ').toUpperCase()}
        </p>
        <p style="font-size: 13px; color: #6b7280; margin-bottom: 6px;">
          <strong>Coordinates:</strong> ${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}
        </p>
        ${location.description ? `
          <p style="font-size: 13px; color: #374151; margin-top: 10px; padding-top: 8px; border-top: 1px solid #e5e7eb;">
            ${location.description}
          </p>
        ` : ''}
      </div>
    `;
  }, []);

  // Optimized marker creation with lazy info windows
  const createOptimizedMarker = useCallback((location: Location) => {
    const marker = new google.maps.Marker({
      position: { lat: location.lat, lng: location.lng },
      map: map,
      title: location.name,
      icon: createMarkerIcon(location.type),
      optimized: true,
      animation: google.maps.Animation.DROP,
    });

    // Create info window lazily (only when needed)
    let infoWindow: google.maps.InfoWindow | null = null;

    marker.addListener("click", () => {
      // Create info window only when clicked (lazy loading)
      if (!infoWindow) {
        infoWindow = new google.maps.InfoWindow({
          content: createInfoWindowContent(location),
          maxWidth: 350,
          pixelOffset: new google.maps.Size(0, -10),
        });
      }
      
      // Close other info windows before opening new one
      markers.forEach(m => {
        const existingInfoWindow = (m as any).infoWindow;
        if (existingInfoWindow) {
          existingInfoWindow.close();
        }
      });

      infoWindow.open(map, marker);
      
      if (onLocationClick) {
        onLocationClick(location);
      }
    });

    // Store info window reference for cleanup
    (marker as any).infoWindow = infoWindow;
    return marker;
  }, [map, onLocationClick, markers, createInfoWindowContent]);

  // Add markers with advanced performance optimizations
  useEffect(() => {
    if (!map) {
      console.log("⏳ OptimizedMapComponent: No map instance yet");
      return;
    }
    
    if (!locations.length) {
      console.log("⏳ OptimizedMapComponent: No locations to display");
      // Clear existing markers if no locations
      markers.forEach((marker) => {
        marker.setMap(null);
        const infoWindow = (marker as any).infoWindow;
        if (infoWindow) {
          infoWindow.close();
        }
      });
      setMarkers([]);
      return;
    }

    console.log(`🗺️ OptimizedMapComponent: Processing ${locations.length} markers...`);
    setIsLoadingMarkers(true);
    MapPerformanceMonitor.start();

    // Clear existing markers efficiently
    markers.forEach((marker) => {
      marker.setMap(null);
      const infoWindow = (marker as any).infoWindow;
      if (infoWindow) {
        infoWindow.close();
      }
    });

    // Create new markers with batch processing
    const newMarkers: google.maps.Marker[] = [];
    
    batchProcessMarkers(
      locations,
      (location) => {
        const marker = createOptimizedMarker(location);
        newMarkers.push(marker);
      },
      15, // Larger batch size for better performance
      () => {
        // All markers processed
        setMarkers(newMarkers);
        setIsLoadingMarkers(false);
        const duration = MapPerformanceMonitor.end("Markers Creation");
        console.log(`✅ OptimizedMapComponent: ${newMarkers.length} markers added in ${duration.toFixed(2)}ms`);
      }
    );
  }, [map, locations, createOptimizedMarker]);

  // Loading state with enhanced UI
  if (!isLoaded) {
    return (
      <div className={`flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 ${className}`}>
        <div className="bg-white rounded-xl shadow-lg border p-8 text-center max-w-sm">
          <div className="relative mb-6">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-6 h-6 bg-blue-600 rounded-full animate-pulse"></div>
            </div>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Loading Map
          </h3>
          <p className="text-gray-600 text-sm">
            Initializing Google Maps with optimizations...
          </p>
          <div className="mt-4 bg-blue-50 rounded-lg p-3">
            <p className="text-blue-700 text-xs">
              🚀 Enhanced performance loading
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <div ref={mapRef} className={`w-full h-full ${className}`} />
      
      {/* Loading overlay for markers */}
      {isLoadingMarkers && (
        <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg border p-3 flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-200 border-t-blue-600"></div>
          <span className="text-sm text-gray-700">Loading markers...</span>
        </div>
      )}
      
      {/* Performance indicator */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-4 right-4 bg-black bg-opacity-75 text-white rounded-lg p-2 text-xs">
          <div>🗺️ Optimized Map</div>
          <div>📍 Markers: {markers.length}</div>
          <div>⚡ Status: {isLoadingMarkers ? 'Loading' : 'Ready'}</div>
        </div>
      )}
    </div>
  );
}
