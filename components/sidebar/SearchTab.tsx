'use client';

import React from 'react';
import { SearchTabProps } from '@/types/map';
import PieChartComponent from '../charts/PieChartComponent';
import BarChartComponent from '../charts/BarChartComponent';

export default function SearchTab({
  filters,
  onFiltersChange,
  statistics,
  onReset
}: SearchTabProps) {
  
  // Prepare chart data
  const pieData = [
    { 
      name: 'Active Trips With Alerts', 
      value: statistics.activeTripsWithAlerts, 
      color: '#3b82f6' 
    },
    { 
      name: 'Active Trips Without Alerts', 
      value: statistics.activeTripsWithoutAlerts, 
      color: '#f97316' 
    }
  ];

  const barData = [
    {
      name: '0',
      'Active Trips': statistics.activeTrips,
      'Active Trips With Alerts': statistics.activeTripsWithAlerts,
      'Active Trips With Communication Lost': statistics.activeTripsWithCommunicationLost
    }
  ];

  const barColors = {
    'Active Trips': '#dc2626',
    'Active Trips With Alerts': '#3b82f6',
    'Active Trips With Communication Lost': '#22c55e'
  };

  const tripCodeOptions = [
    'Trip Code',
    'Driver Name',
    'Vehicle Plate Number',
    'E-locks',
    'Device Id',
    'Entry Port Name',
    'Exit Port Name',
    'Transit Number',
    'Transit Sequence',
    'Transit Date'
  ];

  return (
    <div className="p-4 space-y-4">
      {/* Filter Checkboxes */}
      <div className="space-y-3">
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={filters.showPort}
            onChange={(e) => onFiltersChange({
              ...filters,
              showPort: e.target.checked
            })}
            className="rounded text-blue-600"
          />
          <span className="text-sm">Show Port</span>
        </label>
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={filters.showCheckpost}
            onChange={(e) => onFiltersChange({
              ...filters,
              showCheckpost: e.target.checked
            })}
            className="rounded text-blue-600"
          />
          <span className="text-sm">Show CheckPost/Police Station</span>
        </label>
      </div>

      {/* Trip Code Dropdown */}
      <div>
        <select
          value={filters.tripCode}
          onChange={(e) => onFiltersChange({
            ...filters,
            tripCode: e.target.value
          })}
          className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {tripCodeOptions.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      </div>

      {/* Search Input */}
      <div>
        <input
          type="text"
          value={filters.searchValue}
          onChange={(e) => onFiltersChange({
            ...filters,
            searchValue: e.target.value
          })}
          placeholder="Enter search value..."
          className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Reset Button */}
      <div className="pt-2">
        <button
          onClick={onReset}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors text-sm"
        >
          Reset
        </button>
      </div>

      {/* Charts Section */}
      <div className="border-t pt-4 mt-6">
        <h3 className="font-semibold mb-4 text-gray-800">Trip Statistics</h3>

        {/* Pie Chart */}
        <div className="mb-6">
          <PieChartComponent
            data={pieData}
            title="Active Trips"
            height={200}
            centerValue={statistics.activeTripsWithoutAlerts}
            showLegend={true}
          />
        </div>

        {/* Bar Chart */}
        <div>
          <BarChartComponent
            data={barData}
            title="Route Analytics"
            height={200}
            colors={barColors}
            showLegend={true}
          />
        </div>
      </div>
    </div>
  );
}
