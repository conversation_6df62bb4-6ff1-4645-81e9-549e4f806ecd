'use client';

import React from 'react';
import { AlertsTabProps } from '@/types/map';

export default function AlertsTab({ alerts, onAlertClick }: AlertsTabProps) {
  const getSeverityColor = (severity: 'high' | 'medium' | 'low') => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: 'high' | 'medium' | 'low') => {
    switch (severity) {
      case 'high':
        return '🚨';
      case 'medium':
        return '⚠️';
      case 'low':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  if (alerts.length === 0) {
    return (
      <div className="p-4">
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-4">🔔</div>
          <h3 className="text-lg font-medium text-gray-600 mb-2">No Alerts</h3>
          <p className="text-gray-500 text-sm">
            All systems are running normally. New alerts will appear here.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-800">Recent Alerts</h3>
        <span className="text-sm text-gray-500">
          {alerts.length} alert{alerts.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="space-y-3">
        {alerts.map((alert) => (
          <div
            key={alert.id}
            className={`border rounded-lg p-3 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer ${
              onAlertClick ? 'hover:bg-gray-50' : ''
            }`}
            onClick={() => onAlertClick && onAlertClick(alert)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-lg">{getSeverityIcon(alert.severity)}</span>
                  <h4 className="font-medium text-sm text-gray-900">
                    {alert.title}
                  </h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(alert.severity)}`}>
                    {alert.severity.toUpperCase()}
                  </span>
                </div>
                
                <p className="text-xs text-gray-600 mb-2 leading-relaxed">
                  {alert.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <p className="text-xs text-gray-500">
                    {alert.time}
                  </p>
                  
                  {alert.location && (
                    <p className="text-xs text-blue-600 font-medium">
                      📍 {alert.location.name}
                    </p>
                  )}
                </div>
                
                {alert.type && (
                  <div className="mt-2">
                    <span className="inline-block px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                      {alert.type}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Alert Summary */}
      <div className="mt-6 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Alert Summary</h4>
        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="text-center">
            <div className="text-red-600 font-bold">
              {alerts.filter(a => a.severity === 'high').length}
            </div>
            <div className="text-gray-600">High</div>
          </div>
          <div className="text-center">
            <div className="text-yellow-600 font-bold">
              {alerts.filter(a => a.severity === 'medium').length}
            </div>
            <div className="text-gray-600">Medium</div>
          </div>
          <div className="text-center">
            <div className="text-blue-600 font-bold">
              {alerts.filter(a => a.severity === 'low').length}
            </div>
            <div className="text-gray-600">Low</div>
          </div>
        </div>
      </div>
    </div>
  );
}
