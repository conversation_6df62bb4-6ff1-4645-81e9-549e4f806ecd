'use client';

import React, { useState } from 'react';
import { RoutesTabProps } from '@/types/map';

export default function RoutesTab({
  routes,
  userRoutes,
  selectedRoutes,
  onRoutesChange,
  onRouteFilter
}: RoutesTabProps) {
  const [routeFilter, setRouteFilter] = useState<'my-routes' | 'all'>('my-routes');
  const [routeName, setRouteName] = useState('');
  const [filteredRoutes, setFilteredRoutes] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [localSelectedRoutes, setLocalSelectedRoutes] = useState<string[]>(selectedRoutes);

  // Get route options based on current filter
  const getRouteOptions = () => {
    return routeFilter === 'my-routes' 
      ? userRoutes.map(route => route.name)
      : routes.map(route => route.name);
  };

  const handleFilterChange = (filter: 'my-routes' | 'all') => {
    setRouteFilter(filter);
    onRouteFilter(filter);
    
    // Pre-populate with routes based on filter
    const routeOptions = filter === 'my-routes' 
      ? userRoutes.map(route => route.name)
      : routes.map(route => route.name);
    
    setLocalSelectedRoutes(routeOptions);
    onRoutesChange(routeOptions);
  };

  const handleReset = () => {
    setRouteFilter('my-routes');
    setRouteName('');
    setLocalSelectedRoutes([]);
    setFilteredRoutes([]);
    setShowSuggestions(false);
    onRoutesChange([]);
    onRouteFilter('my-routes');
  };

  const handleRouteSearch = (value: string) => {
    setRouteName(value);
    
    if (value.length > 0) {
      const routeOptions = getRouteOptions();
      const filtered = routeOptions.filter(route =>
        route.toLowerCase().includes(value.toLowerCase()) &&
        !localSelectedRoutes.includes(route)
      );
      setFilteredRoutes(filtered);
      setShowSuggestions(true);
    } else {
      setFilteredRoutes([]);
      setShowSuggestions(false);
    }
  };

  const addRoute = (route: string) => {
    if (!localSelectedRoutes.includes(route)) {
      const newRoutes = [...localSelectedRoutes, route];
      setLocalSelectedRoutes(newRoutes);
      onRoutesChange(newRoutes);
    }
    setRouteName('');
    setShowSuggestions(false);
    setFilteredRoutes([]);
  };

  const removeRoute = (index: number) => {
    const newRoutes = localSelectedRoutes.filter((_, i) => i !== index);
    setLocalSelectedRoutes(newRoutes);
    onRoutesChange(newRoutes);
  };

  return (
    <div className="p-4 space-y-4">
      {/* Route Filter Buttons */}
      <div className="flex w-full gap-1">
        <button
          onClick={() => handleFilterChange('my-routes')}
          className={`flex-1 py-2 px-3 text-sm font-medium rounded-sm transition-colors ${
            routeFilter === 'my-routes'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
          }`}
        >
          My Routes
          <span className="ml-1 text-xs">({userRoutes.length})</span>
        </button>
        <button
          onClick={() => handleFilterChange('all')}
          className={`flex-1 py-2 px-3 text-sm font-medium rounded-sm transition-colors ${
            routeFilter === 'all'
              ? 'bg-green-500 text-white'
              : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
          }`}
        >
          All Routes
          <span className="ml-1 text-xs">({routes.length})</span>
        </button>
        <button
          onClick={handleReset}
          className="flex-1 py-2 px-3 text-sm font-medium bg-red-500 text-white rounded-sm hover:bg-red-600 transition-colors"
        >
          Reset
        </button>
      </div>

      {/* Multi-Select Route Input */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700 text-center">
          Route Name
        </label>

        {/* Selected Routes Tags */}
        <div className="min-h-[120px] border border-gray-300 rounded p-3 bg-white">
          {localSelectedRoutes.length === 0 ? (
            <div className="text-gray-400 text-sm text-center py-8">
              No routes selected. Use the search below to add routes.
            </div>
          ) : (
            <div className="flex flex-wrap gap-2">
              {localSelectedRoutes.map((route, index) => (
                <div
                  key={index}
                  className="flex items-center bg-gray-100 text-gray-800 px-3 py-1 rounded text-sm border hover:bg-gray-200 transition-colors"
                >
                  <span className="mr-2 truncate max-w-[200px]" title={route}>
                    {route}
                  </span>
                  <button
                    onClick={() => removeRoute(index)}
                    className="text-gray-500 hover:text-red-600 font-bold text-lg leading-none"
                    aria-label={`Remove ${route}`}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Search Input */}
        <div className="relative">
          <input
            type="text"
            value={routeName}
            onChange={(e) => handleRouteSearch(e.target.value)}
            onFocus={() => {
              if (routeName.length > 0) {
                handleRouteSearch(routeName);
              }
            }}
            onBlur={() => {
              // Delay hiding suggestions to allow for clicks
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            placeholder="Type to search and add routes..."
            className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />

          {/* Autocomplete Suggestions */}
          {showSuggestions && filteredRoutes.length > 0 && (
            <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto mt-1">
              {filteredRoutes.map((route, index) => (
                <div
                  key={index}
                  onClick={() => addRoute(route)}
                  className="px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  <div className="truncate" title={route}>
                    {route}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Route Statistics */}
      <div className="mt-6 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Route Statistics</h4>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <div className="text-blue-600 font-bold text-lg">
              {localSelectedRoutes.length}
            </div>
            <div className="text-gray-600">Selected Routes</div>
          </div>
          <div>
            <div className="text-green-600 font-bold text-lg">
              {getRouteOptions().length}
            </div>
            <div className="text-gray-600">Available Routes</div>
          </div>
        </div>
      </div>
    </div>
  );
}
