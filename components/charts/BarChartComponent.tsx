'use client';

import React from 'react';
import { <PERSON><PERSON>hart, Bar, <PERSON>Axis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { BarChartComponentProps } from '@/types/map';

export default function BarChartComponent({
  data,
  title,
  height = 200,
  showLegend = true,
  colors = {},
  className = ''
}: BarChartComponentProps) {
  
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center bg-gray-50 rounded-lg ${className}`} style={{ height }}>
        <div className="text-center">
          <div className="text-gray-400 text-2xl mb-2">📊</div>
          <p className="text-gray-500 text-sm">No data available</p>
        </div>
      </div>
    );
  }

  // Extract all numeric keys from data (excluding 'name')
  const dataKeys = Object.keys(data[0] || {}).filter(key => 
    key !== 'name' && typeof data[0][key] === 'number'
  );

  // Default colors for bars
  const defaultColors = [
    '#dc2626', '#3b82f6', '#22c55e', '#f59e0b', '#8b5cf6', 
    '#ef4444', '#06b6d4', '#84cc16', '#f97316', '#ec4899'
  ];

  // Calculate max value for Y-axis
  const maxValue = Math.max(
    ...data.flatMap(item => 
      dataKeys.map(key => typeof item[key] === 'number' ? item[key] : 0)
    )
  );

  // Generate Y-axis ticks
  const generateYAxisTicks = (max: number) => {
    const step = Math.ceil(max / 8);
    const ticks = [];
    for (let i = 0; i <= max; i += step) {
      ticks.push(i);
    }
    if (ticks[ticks.length - 1] < max) {
      ticks.push(max);
    }
    return ticks;
  };

  const yAxisTicks = generateYAxisTicks(maxValue);

  return (
    <div className={`${className}`}>
      {title && (
        <h4 className="text-sm font-medium mb-2 text-center text-gray-700">
          {title}
        </h4>
      )}
      
      <div style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
            <XAxis
              dataKey="name"
              axisLine={true}
              tickLine={true}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <YAxis
              domain={[0, maxValue]}
              ticks={yAxisTicks}
              axisLine={true}
              tickLine={true}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <Tooltip
              formatter={(value: number, name: string) => [
                value.toLocaleString(),
                name
              ]}
              labelFormatter={(label) => `Category: ${label}`}
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '6px',
                fontSize: '12px'
              }}
            />
            
            {dataKeys.map((key, index) => (
              <Bar
                key={key}
                dataKey={key}
                fill={colors[key] || defaultColors[index % defaultColors.length]}
                name={key}
                radius={[2, 2, 0, 0]}
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Custom Legend */}
      {showLegend && dataKeys.length > 0 && (
        <div className="mt-4 space-y-1 text-xs">
          {dataKeys.map((key, index) => {
            const totalValue = data.reduce((sum, item) => 
              sum + (typeof item[key] === 'number' ? item[key] : 0), 0
            );
            
            return (
              <div key={key} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-sm"
                    style={{ 
                      backgroundColor: colors[key] || defaultColors[index % defaultColors.length] 
                    }}
                  ></div>
                  <span className="text-gray-700">{key}</span>
                </div>
                <span className="font-medium text-gray-800">
                  {totalValue.toLocaleString()}
                </span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
