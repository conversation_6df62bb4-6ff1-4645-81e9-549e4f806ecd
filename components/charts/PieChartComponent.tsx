'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Toolt<PERSON> } from 'recharts';
import { ChartComponentProps } from '@/types/map';

export default function PieChartComponent({
  data,
  title,
  height = 200,
  showLegend = true,
  centerValue,
  className = ''
}: ChartComponentProps) {
  
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center bg-gray-50 rounded-lg ${className}`} style={{ height }}>
        <div className="text-center">
          <div className="text-gray-400 text-2xl mb-2">📊</div>
          <p className="text-gray-500 text-sm">No data available</p>
        </div>
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <div className={`${className}`}>
      {title && (
        <h4 className="text-sm font-medium mb-2 text-center text-gray-700">
          {title}
        </h4>
      )}
      
      <div className="flex flex-col items-center">
        <div className="relative" style={{ width: height, height }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                outerRadius={height * 0.35}
                dataKey="value"
                startAngle={90}
                endAngle={450}
              >
                {data.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color || `hsl(${index * 45}, 70%, 50%)`} 
                  />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value: number, name: string) => [
                  `${value} (${((value / total) * 100).toFixed(1)}%)`,
                  name
                ]}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
          
          {/* Center value display */}
          {centerValue !== undefined && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-orange-500 text-white rounded-full flex items-center justify-center font-bold text-lg"
                   style={{ 
                     width: height * 0.25, 
                     height: height * 0.25,
                     fontSize: height * 0.08
                   }}>
                {centerValue}
              </div>
            </div>
          )}
        </div>

        {/* Custom Legend */}
        {showLegend && (
          <div className="mt-4 space-y-2 text-sm w-full">
            {data.map((entry, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-sm"
                    style={{ backgroundColor: entry.color || `hsl(${index * 45}, 70%, 50%)` }}
                  ></div>
                  <span className="text-gray-700 text-xs">{entry.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-xs">{entry.value}</span>
                  <span className="text-gray-500 text-xs">
                    ({((entry.value / total) * 100).toFixed(1)}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Total display */}
        <div className="mt-3 text-center">
          <div className="text-lg font-bold text-gray-800">{total}</div>
          <div className="text-xs text-gray-500">Total</div>
        </div>
      </div>
    </div>
  );
}
