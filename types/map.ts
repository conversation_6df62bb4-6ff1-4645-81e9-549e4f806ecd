// Types for Map Components

export interface Location {
  id: number;
  name: string;
  lat: number;
  lng: number;
  type: LocationType;
  description?: string;
  additionalInfo?: Record<string, any>;
}

export type LocationType = 'airport' | 'landport' | 'police_station' | 'checkpoint' | 'seaport' | 'custom';

export interface MarkerConfig {
  color: string;
  svg: string;
}

export interface MapConfig {
  center: { lat: number; lng: number };
  zoom: number;
  styles?: google.maps.MapTypeStyle[];
}

export interface Alert {
  id: number;
  title: string;
  description: string;
  time: string;
  severity: 'high' | 'medium' | 'low';
  type?: string;
  location?: Location;
}

export interface Route {
  id: string;
  name: string;
  startLocation: Location;
  endLocation: Location;
  waypoints?: Location[];
  distance?: number;
  estimatedTime?: number;
}

export interface TripStatistics {
  activeTrips: number;
  activeTripsWithAlerts: number;
  activeTripsWithoutAlerts: number;
  activeTripsWithCommunicationLost: number;
}

export interface SearchFilters {
  showPort: boolean;
  showCheckpost: boolean;
  tripCode: string;
  searchValue: string;
  locationTypes?: LocationType[];
}

export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

export interface BarChartData {
  name: string;
  [key: string]: string | number;
}

// Props interfaces for components
export interface MapComponentProps {
  locations: Location[];
  config?: Partial<MapConfig>;
  onLocationClick?: (location: Location) => void;
  onMapReady?: (map: google.maps.Map) => void;
  className?: string;
}

export interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  locations: Location[];
  alerts: Alert[];
  routes: Route[];
  statistics: TripStatistics;
  onSearch?: (filters: SearchFilters) => void;
  className?: string;
}

export interface SearchTabProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  statistics: TripStatistics;
  onReset: () => void;
}

export interface AlertsTabProps {
  alerts: Alert[];
  onAlertClick?: (alert: Alert) => void;
}

export interface RoutesTabProps {
  routes: Route[];
  userRoutes: Route[];
  selectedRoutes: string[];
  onRoutesChange: (routes: string[]) => void;
  onRouteFilter: (filter: 'my-routes' | 'all') => void;
}

export interface ChartComponentProps {
  data: ChartData[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  centerValue?: number;
  className?: string;
}

export interface BarChartComponentProps {
  data: BarChartData[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  className?: string;
  colors?: Record<string, string>;
}
