import { Location, Alert, Route, TripStatistics } from '@/types/map';

// Sample locations data
export const sampleLocations: Location[] = [
  {
    id: 1,
    name: 'King <PERSON> International Airport',
    lat: 26.4712,
    lng: 49.7979,
    type: 'airport',
    description: 'Major international airport serving the Eastern Province'
  },
  {
    id: 2,
    name: 'King Abdul<PERSON>z International Airport',
    lat: 21.6796,
    lng: 39.1564,
    type: 'airport',
    description: 'Main airport serving Jeddah and the western region'
  },
  {
    id: 3,
    name: 'King Khalid International Airport',
    lat: 24.9576,
    lng: 46.6988,
    type: 'airport',
    description: 'Primary airport serving Riyadh and central region'
  },
  {
    id: 4,
    name: 'King Fahd Causeway',
    lat: 26.2361,
    lng: 50.1500,
    type: 'landport',
    description: 'Major land crossing connecting Saudi Arabia and Bahrain'
  },
  {
    id: 5,
    name: 'Jeddah Islamic Seaport',
    lat: 21.4858,
    lng: 39.1925,
    type: 'seaport',
    description: 'Largest seaport in Saudi Arabia on the Red Sea'
  },
  {
    id: 6,
    name: 'Dammam Seaport',
    lat: 26.4207,
    lng: 50.1063,
    type: 'seaport',
    description: 'Major seaport on the Arabian Gulf'
  },
  {
    id: 7,
    name: 'Batha Customs Border',
    lat: 25.0000,
    lng: 47.0000,
    type: 'checkpoint',
    description: 'Important customs checkpoint on the northern border'
  },
  {
    id: 8,
    name: 'Khafji Border Checkpoint',
    lat: 28.4292,
    lng: 48.4917,
    type: 'checkpoint',
    description: 'Border checkpoint with Kuwait'
  },
  {
    id: 9,
    name: 'Riyadh Central Police Station',
    lat: 24.7136,
    lng: 46.6753,
    type: 'police_station',
    description: 'Main police station in Riyadh'
  },
  {
    id: 10,
    name: 'Jeddah Police Headquarters',
    lat: 21.5169,
    lng: 39.2192,
    type: 'police_station',
    description: 'Police headquarters for Jeddah region'
  }
];

// Mock alerts data
export const sampleAlerts: Alert[] = [
  {
    id: 1,
    title: 'Speed Alert',
    description: 'Vehicle exceeded speed limit on Highway 40',
    time: '10 mins ago',
    severity: 'high',
    type: 'Speed Violation',
    location: sampleLocations[0]
  },
  {
    id: 2,
    title: 'Geofence Alert',
    description: 'Vehicle left designated route near Jeddah',
    time: '25 mins ago',
    severity: 'medium',
    type: 'Route Deviation',
    location: sampleLocations[1]
  },
  {
    id: 3,
    title: 'E-lock Alert',
    description: 'E-lock tampering detected on container #12345',
    time: '1 hour ago',
    severity: 'high',
    type: 'Security Breach'
  },
  {
    id: 4,
    title: 'Delay Alert',
    description: 'Vehicle is 2 hours behind scheduled arrival',
    time: '2 hours ago',
    severity: 'low',
    type: 'Schedule Delay'
  },
  {
    id: 5,
    title: 'Stop Alert',
    description: 'Unscheduled stop detected for more than 30 minutes',
    time: '3 hours ago',
    severity: 'medium',
    type: 'Unauthorized Stop',
    location: sampleLocations[4]
  },
  {
    id: 6,
    title: 'Communication Lost',
    description: 'Lost GPS signal from vehicle TR-4567',
    time: '4 hours ago',
    severity: 'high',
    type: 'Communication Issue'
  },
  {
    id: 7,
    title: 'Temperature Alert',
    description: 'Cargo temperature exceeded safe limits',
    time: '5 hours ago',
    severity: 'medium',
    type: 'Environmental'
  }
];

// Sample routes data
export const sampleRoutes: Route[] = [
  {
    id: 'route-1',
    name: 'Jeddah Islamic Seaport-King Fahad Intl Airport',
    startLocation: sampleLocations[4], // Jeddah Seaport
    endLocation: sampleLocations[0],   // King Fahd Airport
    distance: 1250,
    estimatedTime: 14
  },
  {
    id: 'route-2',
    name: 'King Fahad Intl Airport-Khafji Border',
    startLocation: sampleLocations[0], // King Fahd Airport
    endLocation: sampleLocations[7],   // Khafji Border
    distance: 180,
    estimatedTime: 2
  },
  {
    id: 'route-3',
    name: 'Batha Customs Border-Jeddah Islamic Seaport',
    startLocation: sampleLocations[6], // Batha Customs
    endLocation: sampleLocations[4],   // Jeddah Seaport
    distance: 950,
    estimatedTime: 11
  },
  {
    id: 'user-1',
    name: 'Riyadh-Dammam Express Route',
    startLocation: sampleLocations[2], // Riyadh Airport
    endLocation: sampleLocations[5],   // Dammam Seaport
    distance: 395,
    estimatedTime: 4
  },
  {
    id: 'user-2',
    name: 'Jeddah-Riyadh Highway',
    startLocation: sampleLocations[1], // Jeddah Airport
    endLocation: sampleLocations[2],   // Riyadh Airport
    distance: 950,
    estimatedTime: 9
  },
  {
    id: 'route-4',
    name: 'Ruqaie Border-Diba Seaport Customs',
    startLocation: sampleLocations[7],
    endLocation: sampleLocations[5],
    distance: 420,
    estimatedTime: 5
  },
  {
    id: 'route-5',
    name: 'Batha Customs Border-Haditha Customs Border',
    startLocation: sampleLocations[6],
    endLocation: sampleLocations[7],
    distance: 280,
    estimatedTime: 3
  },
  {
    id: 'route-6',
    name: 'Al Wadiah Border-Jizan Port',
    startLocation: sampleLocations[6],
    endLocation: sampleLocations[4],
    distance: 1100,
    estimatedTime: 13
  },
  {
    id: 'route-7',
    name: 'Salwa Border-Jubail Port',
    startLocation: sampleLocations[3],
    endLocation: sampleLocations[5],
    distance: 320,
    estimatedTime: 4
  },
  {
    id: 'route-8',
    name: 'Durra Border-Yanbu Port',
    startLocation: sampleLocations[7],
    endLocation: sampleLocations[4],
    distance: 850,
    estimatedTime: 10
  }
];

// Sample trip statistics
export const sampleStatistics: TripStatistics = {
  activeTrips: 1562,
  activeTripsWithAlerts: 11,
  activeTripsWithoutAlerts: 1551,
  activeTripsWithCommunicationLost: 1
};

// Helper function to get user routes
export const getUserRoutes = (): Route[] => {
  return sampleRoutes.filter(route => route.id.startsWith('user-'));
};

// Helper function to get route options for autocomplete
export const getRouteOptions = (): string[] => {
  return sampleRoutes.map(route => route.name);
};

// Helper function to get locations by type
export const getLocationsByType = (type: string): Location[] => {
  return sampleLocations.filter(location => location.type === type);
};
