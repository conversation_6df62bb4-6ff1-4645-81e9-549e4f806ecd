# تحسين مكون الخريطة - دليل الاستخدام

## نظرة عامة

تم تحسين مكون الخريطة الأصلي وتقسيمه إلى مكونات منفصلة وقابلة لإعادة الاستخدام. هذا التحسين يجعل الكود أكثر تنظيماً وسهولة في الصيانة والتطوير.

## الهيكل الجديد

### 📁 المجلدات والملفات

```
├── types/
│   └── map.ts                    # تعريف الأنواع والواجهات
├── components/
│   ├── map/
│   │   └── MapComponent.tsx      # مكون الخريطة الأساسي
│   ├── sidebar/
│   │   ├── Sidebar.tsx           # الشريط الجانبي الرئيسي
│   │   ├── SearchTab.tsx         # تبويب البحث
│   │   ├── AlertsTab.tsx         # تبويب التنبيهات
│   │   └── RoutesTab.tsx         # تبويب المسارات
│   └── charts/
│       ├── PieChartComponent.tsx # مكون الرسم البياني الدائري
│       └── BarChartComponent.tsx # مكون الرسم البياني العمودي
├── hooks/
│   ├── useMapData.ts             # إدارة بيانات الخريطة
│   └── useSidebar.ts             # إدارة حالة الشريط الجانبي
├── utils/
│   └── mapUtils.ts               # وظائف مساعدة للخريطة
├── data/
│   └── mockData.ts               # البيانات التجريبية
└── app/
    └── improved-map/
        └── page.tsx              # الصفحة المحسنة
```

## المكونات الرئيسية

### 1. MapComponent

مكون الخريطة الأساسي الذي يعرض Google Maps مع العلامات.

```tsx
import MapComponent from '@/components/map/MapComponent';

<MapComponent
  locations={locations}
  config={{ center: { lat: 24.7136, lng: 46.6753 }, zoom: 6 }}
  onLocationClick={(location) => console.log(location)}
  onMapReady={(map) => console.log('Map ready')}
/>
```

**الخصائص:**
- `locations`: مصفوفة المواقع المراد عرضها
- `config`: إعدادات الخريطة (اختياري)
- `onLocationClick`: دالة تستدعى عند النقر على موقع
- `onMapReady`: دالة تستدعى عند جاهزية الخريطة

### 2. Sidebar

الشريط الجانبي التفاعلي مع التبويبات المختلفة.

```tsx
import Sidebar from '@/components/sidebar/Sidebar';

<Sidebar
  isOpen={sidebarOpen}
  onClose={() => setSidebarOpen(false)}
  locations={locations}
  alerts={alerts}
  routes={routes}
  statistics={statistics}
  onSearch={(filters) => handleSearch(filters)}
/>
```

### 3. Charts Components

مكونات الرسوم البيانية القابلة لإعادة الاستخدام.

```tsx
import PieChartComponent from '@/components/charts/PieChartComponent';
import BarChartComponent from '@/components/charts/BarChartComponent';

<PieChartComponent
  data={pieData}
  title="Active Trips"
  height={200}
  centerValue={1551}
  showLegend={true}
/>

<BarChartComponent
  data={barData}
  title="Route Analytics"
  height={200}
  colors={{ 'Active Trips': '#dc2626' }}
  showLegend={true}
/>
```

## Hooks المخصصة

### useMapData

إدارة بيانات الخريطة والمرشحات.

```tsx
import { useMapData } from '@/hooks/useMapData';

const {
  locations,
  alerts,
  routes,
  statistics,
  filteredLocations,
  setSearchFilters,
  resetFilters
} = useMapData({
  initialLocations: sampleLocations,
  initialAlerts: sampleAlerts,
  initialRoutes: sampleRoutes,
  initialStatistics: sampleStatistics
});
```

### useSidebar

إدارة حالة الشريط الجانبي.

```tsx
import { useSidebar } from '@/hooks/useSidebar';

const {
  isOpen,
  activeTab,
  openSidebar,
  closeSidebar,
  toggleSidebar,
  setActiveTab,
  openWithTab
} = useSidebar({
  initialOpen: false,
  initialTab: 'search'
});
```

## كيفية التخصيص

### 1. إضافة أنواع مواقع جديدة

في `types/map.ts`:

```tsx
export type LocationType = 'airport' | 'landport' | 'police_station' | 'checkpoint' | 'seaport' | 'custom' | 'new_type';
```

في `utils/mapUtils.ts`:

```tsx
const MARKER_CONFIGS: Record<LocationType, MarkerConfig> = {
  // ... existing configs
  new_type: {
    color: '#your-color',
    svg: `<svg>your-svg-content</svg>`
  }
};
```

### 2. تخصيص البيانات

إنشاء ملف بيانات جديد:

```tsx
// data/customData.ts
import { Location, Alert, Route, TripStatistics } from '@/types/map';

export const customLocations: Location[] = [
  {
    id: 1,
    name: 'موقع مخصص',
    lat: 24.7136,
    lng: 46.6753,
    type: 'custom',
    description: 'وصف الموقع'
  }
];

export const customAlerts: Alert[] = [
  // تنبيهات مخصصة
];

export const customRoutes: Route[] = [
  // مسارات مخصصة
];

export const customStatistics: TripStatistics = {
  activeTrips: 100,
  activeTripsWithAlerts: 5,
  activeTripsWithoutAlerts: 95,
  activeTripsWithCommunicationLost: 0
};
```

### 3. إنشاء صفحة مخصصة

```tsx
// app/custom-map/page.tsx
'use client';

import React from 'react';
import MapComponent from '@/components/map/MapComponent';
import Sidebar from '@/components/sidebar/Sidebar';
import { useMapData } from '@/hooks/useMapData';
import { useSidebar } from '@/hooks/useSidebar';
import { customLocations, customAlerts, customRoutes, customStatistics } from '@/data/customData';

export default function CustomMapPage() {
  const mapData = useMapData({
    initialLocations: customLocations,
    initialAlerts: customAlerts,
    initialRoutes: customRoutes,
    initialStatistics: customStatistics
  });

  const sidebar = useSidebar();

  return (
    <div className="fixed inset-0 w-full h-full">
      <MapComponent
        locations={mapData.filteredLocations}
        onLocationClick={(location) => {
          console.log('Custom location clicked:', location);
          sidebar.openWithTab('search');
        }}
      />
      
      <Sidebar
        isOpen={sidebar.isOpen}
        onClose={sidebar.closeSidebar}
        locations={mapData.locations}
        alerts={mapData.alerts}
        routes={mapData.routes}
        statistics={mapData.statistics}
        onSearch={mapData.setSearchFilters}
      />
    </div>
  );
}
```

## المزايا الجديدة

### ✅ قابلية إعادة الاستخدام
- كل مكون منفصل ويمكن استخدامه في صفحات مختلفة
- إمكانية تخصيص البيانات والإعدادات

### ✅ سهولة الصيانة
- كود منظم ومقسم حسب الوظائف
- فصل المنطق عن واجهة المستخدم

### ✅ قابلية التوسع
- إضافة مكونات جديدة بسهولة
- تخصيص الأنواع والواجهات

### ✅ الأداء المحسن
- تحميل البيانات عند الحاجة
- إدارة الحالة بكفاءة

### ✅ تجربة مستخدم أفضل
- واجهة تفاعلية ومتجاوبة
- رسوم بيانية تفاعلية

## الاستخدام

1. انسخ المجلدات والملفات إلى مشروعك
2. تأكد من تثبيت المكتبات المطلوبة:
   ```bash
   npm install recharts lucide-react
   ```
3. قم بتعديل البيانات في `data/mockData.ts` حسب احتياجاتك
4. استخدم الصفحة المحسنة: `/improved-map`

## مثال كامل

راجع الملف `app/improved-map/page.tsx` لمثال كامل على كيفية استخدام جميع المكونات معاً.
