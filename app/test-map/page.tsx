"use client";

import React, { useEffect, useRef, useState } from 'react';

export default function TestMapPage() {
  const mapRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if Google Maps is already loaded
    if (window.google && window.google.maps) {
      setIsLoaded(true);
      return;
    }

    // Load Google Maps API
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      console.log('✅ Google Maps API loaded successfully');
      setIsLoaded(true);
    };

    script.onerror = (e) => {
      console.error('❌ Failed to load Google Maps API:', e);
      setError('Failed to load Google Maps API');
    };

    document.head.appendChild(script);

    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  useEffect(() => {
    if (!isLoaded || !mapRef.current) return;

    try {
      console.log('🗺️ Creating map instance...');
      const map = new google.maps.Map(mapRef.current, {
        center: { lat: 24.7136, lng: 46.6753 }, // Riyadh
        zoom: 10,
      });

      // Add a marker
      new google.maps.Marker({
        position: { lat: 24.7136, lng: 46.6753 },
        map: map,
        title: 'Riyadh',
      });

      console.log('✅ Map created successfully');
    } catch (err) {
      console.error('❌ Error creating map:', err);
      setError('Error creating map: ' + (err as Error).message);
    }
  }, [isLoaded]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md">
          <div className="text-red-500 text-4xl mb-4 text-center">❌</div>
          <h2 className="text-xl font-bold text-gray-900 mb-2 text-center">
            Map Loading Error
          </h2>
          <p className="text-gray-600 text-center mb-4">{error}</p>
          <div className="text-sm text-gray-500 text-center">
            <p>API Key: {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 'Present' : 'Missing'}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="w-full mt-4 bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-center">Loading Google Maps...</p>
          <div className="text-sm text-gray-500 text-center mt-2">
            <p>API Key: {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 'Present' : 'Missing'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="bg-white shadow-sm border-b p-4">
        <h1 className="text-2xl font-bold text-gray-900">Test Map Page</h1>
        <p className="text-gray-600">Simple Google Maps test</p>
      </div>
      <div 
        ref={mapRef} 
        className="w-full h-96"
        style={{ minHeight: 'calc(100vh - 100px)' }}
      />
    </div>
  );
}
