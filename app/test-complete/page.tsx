"use client";

import React, { useEffect } from "react";
import MapComponent from "@/components/map/MapComponent";
import Sidebar from "@/components/sidebar/Sidebar";
import { useMapData } from "@/hooks/useMapData";
import { useSidebar } from "@/hooks/useSidebar";
import {
  sampleLocations,
  sampleAlerts,
  sampleRoutes,
  sampleStatistics,
} from "@/data/mockData";
import { Location, SearchFilters } from "@/types/map";

export default function TestCompletePage() {
  // Initialize map data with sample data
  const {
    locations,
    alerts,
    routes,
    statistics,
    filteredLocations,
    isLoading,
    error,
    setSearchFilters,
  } = useMapData({
    initialLocations: sampleLocations,
    initialAlerts: sampleAlerts,
    initialRoutes: sampleRoutes,
    initialStatistics: sampleStatistics,
  });

  // Initialize sidebar state
  const {
    isOpen: sidebarOpen,
    closeSidebar,
    toggleSidebar,
    openWithTab,
  } = useSidebar({
    initialOpen: false,
    initialTab: "search",
  });

  // Log data on component mount
  useEffect(() => {
    console.log("🚀 TestComplete: Component mounted");
    console.log("📍 Total locations:", locations.length);
    console.log("🚨 Total alerts:", alerts.length);
    console.log("🛣️ Total routes:", routes.length);
    console.log("📊 Statistics:", statistics);
  }, [locations, alerts, routes, statistics]);

  // Log filtered locations when they change
  useEffect(() => {
    console.log("🔍 Filtered locations:", filteredLocations.length);
  }, [filteredLocations]);

  // Handle location click on map
  const handleLocationClick = (location: Location) => {
    console.log("📍 Location clicked:", location);
    openWithTab("search");
  };

  // Handle search filters change
  const handleSearchFiltersChange = (filters: SearchFilters) => {
    console.log("🔍 Filters changed:", filters);
    setSearchFilters(filters);
  };

  // Handle map ready
  const handleMapReady = (map: google.maps.Map) => {
    console.log("🗺️ Map is ready:", map);
  };

  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-100">
        <div className="bg-white rounded-lg border p-8 text-center max-w-md">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Error Loading Map
          </h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 w-full h-full">
      {/* Header */}
      <div className="fixed top-0 left-0 right-0 bg-white shadow-sm border-b z-50 p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Complete Test Page</h1>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              Locations: {filteredLocations.length}/{locations.length}
            </span>
            <button
              onClick={toggleSidebar}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
            >
              {sidebarOpen ? "Close Sidebar" : "Open Sidebar"}
            </button>
          </div>
        </div>
      </div>

      {/* Map Component */}
      <div className="absolute inset-0 pt-20">
        <MapComponent
          locations={filteredLocations}
          onLocationClick={handleLocationClick}
          onMapReady={handleMapReady}
          className="absolute inset-0 w-full h-full"
        />
      </div>

      {/* Sidebar Component */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={closeSidebar}
        onToggle={toggleSidebar}
        locations={locations}
        alerts={alerts}
        routes={routes}
        statistics={statistics}
        onSearch={handleSearchFiltersChange}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
          style={{ zIndex: 9999 }}
        >
          <div className="bg-white rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      )}

      {/* Debug Info */}
      <div
        className="fixed bottom-4 left-4 bg-black bg-opacity-75 text-white p-3 rounded text-xs max-w-xs"
        style={{ zIndex: 1000 }}
      >
        <div className="space-y-1">
          <div>🗺️ Map: Ready</div>
          <div>📍 Total Locations: {locations.length}</div>
          <div>🔍 Filtered: {filteredLocations.length}</div>
          <div>🚨 Alerts: {alerts.length}</div>
          <div>🛣️ Routes: {routes.length}</div>
          <div>📊 Active Trips: {statistics.activeTrips}</div>
          <div>🔧 Sidebar: {sidebarOpen ? "Open" : "Closed"}</div>
          <div>⚡ Status: Active</div>
        </div>
      </div>

      {/* Test Controls */}
      <div
        className="fixed top-24 left-4 bg-white shadow-lg rounded p-3 text-xs"
        style={{ zIndex: 1000 }}
      >
        <h4 className="font-bold mb-2">Test Controls</h4>
        <div className="space-y-2">
          <button
            onClick={() => console.log("Current locations:", filteredLocations)}
            className="block w-full text-left bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded text-xs"
          >
            Log Locations
          </button>
          <button
            onClick={() => console.log("Current filters:", setSearchFilters)}
            className="block w-full text-left bg-green-100 hover:bg-green-200 px-2 py-1 rounded text-xs"
          >
            Log Filters
          </button>
          <button
            onClick={() => {
              const testFilters: SearchFilters = {
                showPort: false,
                showCheckpost: true,
                tripCode: "Trip Code",
                searchValue: "airport"
              };
              handleSearchFiltersChange(testFilters);
            }}
            className="block w-full text-left bg-yellow-100 hover:bg-yellow-200 px-2 py-1 rounded text-xs"
          >
            Test Filter
          </button>
        </div>
      </div>
    </div>
  );
}
