"use client";

import React, { useEffect, useState } from 'react';

export default function DebugMapPage() {
  const [apiKey, setApiKey] = useState<string>('');
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string>('');
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    const key = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    setApiKey(key || 'NOT_SET');
    addLog(`API Key: ${key ? 'Present' : 'Missing'}`);

    if (!key) {
      setError('Google Maps API key is not set');
      return;
    }

    // Check if Google Maps is already loaded
    if (window.google && window.google.maps) {
      addLog('Google Maps already loaded');
      setIsLoaded(true);
      return;
    }

    addLog('Loading Google Maps API...');
    
    // Load Google Maps API
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${key}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      addLog('✅ Google Maps API loaded successfully');
      setIsLoaded(true);
    };

    script.onerror = (e) => {
      addLog('❌ Failed to load Google Maps API');
      setError('Failed to load Google Maps API');
    };

    document.head.appendChild(script);

    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  const testMapCreation = () => {
    if (!isLoaded) {
      addLog('❌ Cannot test map - API not loaded');
      return;
    }

    try {
      addLog('🗺️ Testing map creation...');
      const testDiv = document.createElement('div');
      testDiv.style.width = '100px';
      testDiv.style.height = '100px';
      document.body.appendChild(testDiv);

      const map = new google.maps.Map(testDiv, {
        center: { lat: 24.7136, lng: 46.6753 },
        zoom: 10,
      });

      addLog('✅ Map created successfully');
      document.body.removeChild(testDiv);
    } catch (err) {
      addLog(`❌ Map creation failed: ${(err as Error).message}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Google Maps Debug Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Status Card */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Status</h2>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">API Key:</span>
                <span className={`font-mono text-sm ${apiKey === 'NOT_SET' ? 'text-red-500' : 'text-green-500'}`}>
                  {apiKey === 'NOT_SET' ? 'NOT SET' : 'SET'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">API Loaded:</span>
                <span className={`${isLoaded ? 'text-green-500' : 'text-red-500'}`}>
                  {isLoaded ? '✅ Yes' : '❌ No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Error:</span>
                <span className={`${error ? 'text-red-500' : 'text-green-500'}`}>
                  {error || '✅ None'}
                </span>
              </div>
            </div>
            
            <button
              onClick={testMapCreation}
              disabled={!isLoaded}
              className="mt-4 w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Test Map Creation
            </button>
          </div>

          {/* API Key Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">API Key Info</h2>
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-gray-600">Key (masked):</span>
                <div className="font-mono bg-gray-100 p-2 rounded mt-1 break-all">
                  {apiKey ? `${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 5)}` : 'NOT SET'}
                </div>
              </div>
              <div>
                <span className="text-gray-600">Environment:</span>
                <div className="font-mono bg-gray-100 p-2 rounded mt-1">
                  {process.env.NODE_ENV}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Logs */}
        <div className="mt-6 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Debug Logs</h2>
          <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-64 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="text-gray-500">No logs yet...</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
          <button
            onClick={() => setLogs([])}
            className="mt-2 text-sm text-gray-500 hover:text-gray-700"
          >
            Clear Logs
          </button>
        </div>

        {/* Test URLs */}
        <div className="mt-6 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test URLs</h2>
          <div className="space-y-2 text-sm">
            <div>
              <span className="text-gray-600">Direct API Test:</span>
              <a
                href={`https://maps.googleapis.com/maps/api/js?key=${apiKey}`}
                target="_blank"
                rel="noopener noreferrer"
                className="block text-blue-500 hover:text-blue-700 font-mono bg-gray-100 p-2 rounded mt-1 break-all"
              >
                https://maps.googleapis.com/maps/api/js?key={apiKey}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
