"use client";

import React, { useEffect, useState } from "react";
import OptimizedMapComponent from "@/components/map/OptimizedMapComponent";
import Sidebar from "@/components/sidebar/Sidebar";
import { useMapData } from "@/hooks/useMapData";
import { useSidebar } from "@/hooks/useSidebar";
import {
  sampleLocations,
  sampleAlerts,
  sampleRoutes,
  sampleStatistics,
} from "@/data/mockData";
import { Location, SearchFilters } from "@/types/map";
import { MapPerformanceMonitor } from "@/utils/optimizedGoogleMaps";

export default function OptimizedMapPage() {
  const [performanceMetrics, setPerformanceMetrics] = useState<{
    mapLoad: number;
    markersLoad: number;
    totalLoad: number;
  }>({ mapLoad: 0, markersLoad: 0, totalLoad: 0 });

  // Initialize map data with sample data
  const {
    locations,
    alerts,
    routes,
    statistics,
    filteredLocations,
    isLoading,
    error,
    setSearchFilters,
  } = useMapData({
    initialLocations: sampleLocations,
    initialAlerts: sampleAlerts,
    initialRoutes: sampleRoutes,
    initialStatistics: sampleStatistics,
  });

  // Initialize sidebar state
  const {
    isOpen: sidebarOpen,
    closeSidebar,
    toggleSidebar,
    openWithTab,
  } = useSidebar({
    initialOpen: false,
    initialTab: "search",
  });

  // Performance monitoring
  useEffect(() => {
    console.log("🚀 OptimizedMapPage: Component mounted");
    console.log("📍 Total locations:", locations.length);
    console.log("🚨 Total alerts:", alerts.length);
    console.log("🛣️ Total routes:", routes.length);
    console.log("📊 Statistics:", statistics);
    
    MapPerformanceMonitor.start();
  }, [locations, alerts, routes, statistics]);

  // Log filtered locations when they change
  useEffect(() => {
    console.log("🔍 Filtered locations:", filteredLocations.length);
    if (filteredLocations.length > 0) {
      const totalTime = MapPerformanceMonitor.end("Total Page Load");
      setPerformanceMetrics(prev => ({ ...prev, totalLoad: totalTime }));
    }
  }, [filteredLocations]);

  // Handle location click on map
  const handleLocationClick = (location: Location) => {
    console.log("📍 Location clicked:", location);
    openWithTab("search");
  };

  // Handle search filters change
  const handleSearchFiltersChange = (filters: SearchFilters) => {
    console.log("🔍 Filters changed:", filters);
    MapPerformanceMonitor.start();
    setSearchFilters(filters);
  };

  // Handle map ready
  const handleMapReady = (map: google.maps.Map) => {
    console.log("🗺️ Optimized map is ready:", map);
    const mapLoadTime = MapPerformanceMonitor.end("Map Ready");
    setPerformanceMetrics(prev => ({ ...prev, mapLoad: mapLoadTime }));
  };

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-50">
        <div className="bg-white rounded-xl shadow-lg border p-8 text-center max-w-md">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Error Loading Optimized Map
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      {/* Map Component - Full screen but respects layout */}
      <div className="fixed inset-0 top-32 w-full h-full">
        <OptimizedMapComponent
          locations={filteredLocations}
          onLocationClick={handleLocationClick}
          onMapReady={handleMapReady}
          className="absolute inset-0 w-full h-full"
        />
      </div>

      {/* Sidebar Component */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={closeSidebar}
        onToggle={toggleSidebar}
        locations={locations}
        alerts={alerts}
        routes={routes}
        statistics={statistics}
        onSearch={handleSearchFiltersChange}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
          style={{ zIndex: 9999 }}
        >
          <div className="bg-white rounded-xl p-8 text-center shadow-2xl">
            <div className="relative mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 bg-blue-600 rounded-full animate-pulse"></div>
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Optimized Map</h3>
            <p className="text-gray-600">Enhanced performance loading...</p>
          </div>
        </div>
      )}

      {/* Performance Metrics (Development Only) */}
      {process.env.NODE_ENV === "development" && (
        <div
          className="fixed bottom-4 left-4 bg-black bg-opacity-90 text-white p-4 rounded-lg text-xs max-w-xs"
          style={{ zIndex: 1000 }}
        >
          <div className="space-y-1">
            <div className="text-green-400 font-bold">🚀 Performance Metrics</div>
            <div>🗺️ Map Load: {performanceMetrics.mapLoad.toFixed(2)}ms</div>
            <div>📍 Locations: {filteredLocations.length}/{locations.length}</div>
            <div>🚨 Alerts: {alerts.length}</div>
            <div>🛣️ Routes: {routes.length}</div>
            <div>📊 Active Trips: {statistics.activeTrips}</div>
            <div>🔧 Sidebar: {sidebarOpen ? "Open" : "Closed"}</div>
            <div className="text-green-400">⚡ Optimized: Active</div>
          </div>
        </div>
      )}

      {/* Enhanced Test Controls */}
      {process.env.NODE_ENV === "development" && (
        <div
          className="fixed top-4 left-4 bg-white shadow-xl rounded-lg p-4 text-sm border"
          style={{ zIndex: 1000 }}
        >
          <h4 className="font-bold mb-3 text-gray-800 flex items-center">
            🧪 Optimized Test Controls
          </h4>
          <div className="space-y-2">
            <button
              onClick={() => {
                console.log("Current locations:", filteredLocations);
                console.log("Performance metrics:", performanceMetrics);
              }}
              className="block w-full text-left bg-blue-100 hover:bg-blue-200 px-3 py-2 rounded text-xs transition-colors"
            >
              📍 Log Performance Data
            </button>
            <button
              onClick={() => {
                const testFilters: SearchFilters = {
                  showPort: false,
                  showCheckpost: true,
                  tripCode: "Trip Code",
                  searchValue: "airport"
                };
                handleSearchFiltersChange(testFilters);
              }}
              className="block w-full text-left bg-yellow-100 hover:bg-yellow-200 px-3 py-2 rounded text-xs transition-colors"
            >
              🔍 Test Airport Filter
            </button>
            <button
              onClick={() => {
                const resetFilters: SearchFilters = {
                  showPort: true,
                  showCheckpost: true,
                  tripCode: "Trip Code",
                  searchValue: ""
                };
                handleSearchFiltersChange(resetFilters);
              }}
              className="block w-full text-left bg-green-100 hover:bg-green-200 px-3 py-2 rounded text-xs transition-colors"
            >
              🔄 Reset Filters
            </button>
            <button
              onClick={toggleSidebar}
              className="block w-full text-left bg-purple-100 hover:bg-purple-200 px-3 py-2 rounded text-xs transition-colors"
            >
              🔧 Toggle Sidebar
            </button>
            <button
              onClick={() => {
                MapPerformanceMonitor.start();
                setTimeout(() => {
                  const testTime = MapPerformanceMonitor.end("Test Measurement");
                  alert(`Test completed in ${testTime.toFixed(2)}ms`);
                }, 1000);
              }}
              className="block w-full text-left bg-red-100 hover:bg-red-200 px-3 py-2 rounded text-xs transition-colors"
            >
              ⏱️ Performance Test
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
