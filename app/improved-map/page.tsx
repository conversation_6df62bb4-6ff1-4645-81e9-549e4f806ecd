"use client";

import React from "react";
import MapComponent from "@/components/map/MapComponent";
import Sidebar from "@/components/sidebar/Sidebar";
import { useMapData } from "@/hooks/useMapData";
import { useSidebar } from "@/hooks/useSidebar";
import {
  sampleLocations,
  sampleAlerts,
  sampleRoutes,
  sampleStatistics,
} from "@/data/mockData";
import { Location, SearchFilters } from "@/types/map";

export default function ImprovedMapPage() {
  // Initialize map data with sample data
  const {
    locations,
    alerts,
    routes,
    statistics,
    filteredLocations,
    isLoading,
    error,
    setSearchFilters,
  } = useMapData({
    initialLocations: sampleLocations,
    initialAlerts: sampleAlerts,
    initialRoutes: sampleRoutes,
    initialStatistics: sampleStatistics,
  });

  // Initialize sidebar state
  const {
    isOpen: sidebarOpen,
    closeSidebar,
    toggleSidebar,
    openWithTab,
  } = useSidebar({
    initialOpen: false,
    initialTab: "search",
  });

  // Handle location click on map
  const handleLocationClick = (location: Location) => {
    console.log("Location clicked:", location);
    // You can add custom logic here, such as:
    // - Show location details
    // - Open sidebar with location info
    // - Filter alerts for this location
    openWithTab("search");
  };

  // Handle search filters change
  const handleSearchFiltersChange = (filters: SearchFilters) => {
    console.log("🔍 Filters changed:", filters);
    setSearchFilters(filters);
  };

  // Handle map ready
  const handleMapReady = (map: google.maps.Map) => {
    console.log("Map is ready:", map);
    // You can add custom map configurations here
  };

  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-100">
        <div className="bg-white rounded-lg border p-8 text-center max-w-md">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Error Loading Map
          </h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 w-full h-full">
      {/* Map Component */}
      <MapComponent
        locations={filteredLocations}
        onLocationClick={handleLocationClick}
        onMapReady={handleMapReady}
        className="absolute inset-0 w-full h-full"
      />

      {/* Sidebar Component */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={closeSidebar}
        onToggle={toggleSidebar}
        locations={locations}
        alerts={alerts}
        routes={routes}
        statistics={statistics}
        onSearch={handleSearchFiltersChange}
      />

      {/* Toggle Button is now integrated in Sidebar component */}

      {/* Loading Overlay */}
      {isLoading && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
          style={{ zIndex: 9999 }}
        >
          <div className="bg-white rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      )}

      {/* Debug Info (only in development) */}
      {process.env.NODE_ENV === "development" && (
        <div
          className="fixed bottom-4 left-4 bg-black bg-opacity-75 text-white p-2 rounded text-xs"
          style={{ zIndex: 1000 }}
        >
          <div>
            Locations: {filteredLocations.length}/{locations.length}
          </div>
          <div>Alerts: {alerts.length}</div>
          <div>Routes: {routes.length}</div>
          <div>Sidebar: {sidebarOpen ? "Open" : "Closed"}</div>
          <div>Sidebar Status: Active</div>
        </div>
      )}
    </div>
  );
}
