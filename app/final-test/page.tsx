"use client";

import React, { useEffect } from "react";
import MapComponent from "@/components/map/MapComponent";
import Sidebar from "@/components/sidebar/Sidebar";
import { useMapData } from "@/hooks/useMapData";
import { useSidebar } from "@/hooks/useSidebar";
import {
  sampleLocations,
  sampleAlerts,
  sampleRoutes,
  sampleStatistics,
} from "@/data/mockData";
import { Location, SearchFilters } from "@/types/map";

export default function FinalTestPage() {
  // Initialize map data with sample data
  const {
    locations,
    alerts,
    routes,
    statistics,
    filteredLocations,
    isLoading,
    error,
    setSearchFilters,
  } = useMapData({
    initialLocations: sampleLocations,
    initialAlerts: sampleAlerts,
    initialRoutes: sampleRoutes,
    initialStatistics: sampleStatistics,
  });

  // Initialize sidebar state
  const {
    isOpen: sidebarOpen,
    closeSidebar,
    toggleSidebar,
    openWithTab,
  } = useSidebar({
    initialOpen: false,
    initialTab: "search",
  });

  // Log data on component mount
  useEffect(() => {
    console.log("🚀 FinalTest: Component mounted");
    console.log("📍 Total locations:", locations.length);
    console.log("🚨 Total alerts:", alerts.length);
    console.log("🛣️ Total routes:", routes.length);
    console.log("📊 Statistics:", statistics);
  }, [locations, alerts, routes, statistics]);

  // Log filtered locations when they change
  useEffect(() => {
    console.log("🔍 Filtered locations:", filteredLocations.length);
    console.log("🔍 Filtered data:", filteredLocations);
  }, [filteredLocations]);

  // Handle location click on map
  const handleLocationClick = (location: Location) => {
    console.log("📍 Location clicked:", location);
    openWithTab("search");
  };

  // Handle search filters change
  const handleSearchFiltersChange = (filters: SearchFilters) => {
    console.log("🔍 Filters changed:", filters);
    setSearchFilters(filters);
  };

  // Handle map ready
  const handleMapReady = (map: google.maps.Map) => {
    console.log("🗺️ Map is ready:", map);
  };

  if (error) {
    return (
      <div className="container mx-auto px-6 py-8">
        <div className="bg-white rounded-lg border p-8 text-center max-w-md mx-auto">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Error Loading Map
          </h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      {/* Page Header */}
      <div className="bg-white shadow-sm border-b p-4 mb-4">
        <div className="container mx-auto px-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Final Test - Map with Sidebar</h1>
              <p className="text-gray-600 text-sm">
                Testing complete map functionality with filters and sidebar
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Locations: {filteredLocations.length}/{locations.length}
              </span>
              <button
                onClick={toggleSidebar}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
              >
                {sidebarOpen ? "Close Sidebar" : "Open Sidebar"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="relative" style={{ height: "calc(100vh - 300px)" }}>
        <MapComponent
          locations={filteredLocations}
          onLocationClick={handleLocationClick}
          onMapReady={handleMapReady}
          className="w-full h-full"
        />
      </div>

      {/* Sidebar Component */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={closeSidebar}
        onToggle={toggleSidebar}
        locations={locations}
        alerts={alerts}
        routes={routes}
        statistics={statistics}
        onSearch={handleSearchFiltersChange}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
          style={{ zIndex: 9999 }}
        >
          <div className="bg-white rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      )}

      {/* Status Panel */}
      <div className="fixed bottom-4 left-4 bg-white shadow-lg rounded p-4 text-sm max-w-xs border">
        <h4 className="font-bold mb-2 text-gray-800">System Status</h4>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span>🗺️ Map:</span>
            <span className="text-green-600">Ready</span>
          </div>
          <div className="flex justify-between">
            <span>📍 Locations:</span>
            <span>{filteredLocations.length}/{locations.length}</span>
          </div>
          <div className="flex justify-between">
            <span>🚨 Alerts:</span>
            <span>{alerts.length}</span>
          </div>
          <div className="flex justify-between">
            <span>🛣️ Routes:</span>
            <span>{routes.length}</span>
          </div>
          <div className="flex justify-between">
            <span>📊 Active Trips:</span>
            <span>{statistics.activeTrips}</span>
          </div>
          <div className="flex justify-between">
            <span>🔧 Sidebar:</span>
            <span className={sidebarOpen ? "text-green-600" : "text-gray-500"}>
              {sidebarOpen ? "Open" : "Closed"}
            </span>
          </div>
        </div>
      </div>

      {/* Test Controls */}
      <div className="fixed bottom-4 right-4 bg-white shadow-lg rounded p-4 text-sm max-w-xs border">
        <h4 className="font-bold mb-2 text-gray-800">Test Controls</h4>
        <div className="space-y-2">
          <button
            onClick={() => console.log("Current locations:", filteredLocations)}
            className="block w-full text-left bg-blue-100 hover:bg-blue-200 px-3 py-2 rounded text-xs"
          >
            📍 Log Locations
          </button>
          <button
            onClick={() => {
              const testFilters: SearchFilters = {
                showPort: false,
                showCheckpost: true,
                tripCode: "Trip Code",
                searchValue: "airport"
              };
              handleSearchFiltersChange(testFilters);
            }}
            className="block w-full text-left bg-yellow-100 hover:bg-yellow-200 px-3 py-2 rounded text-xs"
          >
            🔍 Test Airport Filter
          </button>
          <button
            onClick={() => {
              const resetFilters: SearchFilters = {
                showPort: true,
                showCheckpost: true,
                tripCode: "Trip Code",
                searchValue: ""
              };
              handleSearchFiltersChange(resetFilters);
            }}
            className="block w-full text-left bg-green-100 hover:bg-green-200 px-3 py-2 rounded text-xs"
          >
            🔄 Reset Filters
          </button>
          <button
            onClick={toggleSidebar}
            className="block w-full text-left bg-purple-100 hover:bg-purple-200 px-3 py-2 rounded text-xs"
          >
            🔧 Toggle Sidebar
          </button>
        </div>
      </div>
    </div>
  );
}
